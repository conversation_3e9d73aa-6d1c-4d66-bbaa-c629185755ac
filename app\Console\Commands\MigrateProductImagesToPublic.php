<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Image;
use Illuminate\Support\Facades\File;

class MigrateProductImagesToPublic extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:product-images-to-public';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate product images from storage to public folder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration of product images to public folder...');

        // Create public uploads directory if it doesn't exist
        $publicUploadsDir = public_path('uploads/products');
        if (!File::exists($publicUploadsDir)) {
            File::makeDirectory($publicUploadsDir, 0755, true);
            $this->info('Created uploads/products directory in public folder');
        }

        // Get all product images
        $productImages = Image::where('imageable_type', 'App\Models\Product')->get();
        
        $migratedCount = 0;
        $errorCount = 0;

        foreach ($productImages as $image) {
            try {
                // Check if image path starts with 'products/' (old storage path)
                if (str_starts_with($image->path, 'products/')) {
                    $oldPath = storage_path('app/public/' . $image->path);
                    
                    if (File::exists($oldPath)) {
                        // Generate new filename
                        $extension = pathinfo($image->path, PATHINFO_EXTENSION);
                        $newFilename = 'product_' . time() . '_' . $image->id . '.' . $extension;
                        $newPath = $publicUploadsDir . '/' . $newFilename;
                        
                        // Copy file to new location
                        if (File::copy($oldPath, $newPath)) {
                            // Update database with new path
                            $image->path = 'uploads/products/' . $newFilename;
                            $image->save();
                            
                            $this->info("Migrated: {$image->original_name} -> uploads/products/{$newFilename}");
                            $migratedCount++;
                            
                            // Optionally delete old file (uncomment if you want to clean up)
                            // File::delete($oldPath);
                        } else {
                            $this->error("Failed to copy: {$oldPath}");
                            $errorCount++;
                        }
                    } else {
                        $this->warn("File not found: {$oldPath}");
                        $errorCount++;
                    }
                } else {
                    $this->info("Skipping (already migrated): {$image->path}");
                }
            } catch (\Exception $e) {
                $this->error("Error processing image ID {$image->id}: " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->info("\nMigration completed!");
        $this->info("Migrated: {$migratedCount} images");
        $this->info("Errors: {$errorCount}");
        
        if ($errorCount === 0) {
            $this->info("\n✅ All product images have been successfully migrated to the public folder!");
            $this->info("You can now safely delete the old storage/app/public/products folder if desired.");
        } else {
            $this->warn("\n⚠️  Some images had errors during migration. Please check the logs above.");
        }

        return 0;
    }
}

<?php
/**
 * <PERSON><PERSON>t to migrate product images from storage to public folder
 * Run this from the project root: php migrate_product_images.php
 */

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Image;
use Illuminate\Support\Facades\File;

echo "Starting migration of product images to public folder...\n";
echo "=====================================================\n\n";

// Create public uploads directory if it doesn't exist
$publicUploadsDir = public_path('uploads/products');
if (!File::exists($publicUploadsDir)) {
    File::makeDirectory($publicUploadsDir, 0755, true);
    echo "✓ Created uploads/products directory in public folder\n\n";
} else {
    echo "✓ uploads/products directory already exists\n\n";
}

// Get all product images
$productImages = Image::where('imageable_type', 'App\Models\Product')->get();

echo "Found " . $productImages->count() . " product images to process\n\n";

$migratedCount = 0;
$errorCount = 0;
$skippedCount = 0;

foreach ($productImages as $image) {
    try {
        // Check if image path starts with 'products/' (old storage path)
        if (str_starts_with($image->path, 'products/')) {
            $oldPath = storage_path('app/public/' . $image->path);
            
            if (File::exists($oldPath)) {
                // Generate new filename
                $extension = pathinfo($image->path, PATHINFO_EXTENSION);
                $newFilename = 'product_' . time() . '_' . $image->id . '.' . $extension;
                $newPath = $publicUploadsDir . '/' . $newFilename;
                
                // Copy file to new location
                if (File::copy($oldPath, $newPath)) {
                    // Update database with new path
                    $image->path = 'uploads/products/' . $newFilename;
                    $image->save();
                    
                    echo "✓ Migrated: {$image->original_name} -> uploads/products/{$newFilename}\n";
                    $migratedCount++;
                    
                    // Small delay to ensure unique timestamps
                    usleep(1000);
                } else {
                    echo "✗ Failed to copy: {$oldPath}\n";
                    $errorCount++;
                }
            } else {
                echo "⚠ File not found: {$oldPath}\n";
                $errorCount++;
            }
        } else {
            echo "- Skipping (already migrated): {$image->path}\n";
            $skippedCount++;
        }
    } catch (\Exception $e) {
        echo "✗ Error processing image ID {$image->id}: " . $e->getMessage() . "\n";
        $errorCount++;
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Migration completed!\n";
echo "Migrated: {$migratedCount} images\n";
echo "Skipped: {$skippedCount} images\n";
echo "Errors: {$errorCount}\n";

if ($errorCount === 0) {
    echo "\n✅ All product images have been successfully migrated to the public folder!\n";
    echo "You can now safely delete the old storage/app/public/products folder if desired.\n";
} else {
    echo "\n⚠️  Some images had errors during migration. Please check the logs above.\n";
}

echo "\nDone!\n";

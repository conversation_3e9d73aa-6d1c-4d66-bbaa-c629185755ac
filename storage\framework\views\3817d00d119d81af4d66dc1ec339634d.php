

<?php $__env->startSection('title', 'Welcome to Our Furniture Store'); ?>
<?php $__env->startSection('meta_description', 'Discover our exclusive collection of high-quality furniture for your home. Find sofas, beds, dining sets, and more at competitive prices.'); ?>
<?php $__env->startSection('meta_keywords', 'furniture, home decor, sofas, beds, dining sets, chairs, tables'); ?>

<?php $__env->startSection('content'); ?>

    <main class="main">

        <!-- breadcrumb -->
        <div class="site-breadcrumb">
            <div class="site-breadcrumb-bg" style="background:#20202C"></div>
            <div class="container">
                <div class="site-breadcrumb-wrap">
                    <h4 class="breadcrumb-title"><?php echo e($category->name); ?></h4>
                    <ul class="breadcrumb-menu">
                        <li><a href="<?php echo e(url('/')); ?>"><i class="far fa-home"></i> Home</a></li>
                        <li class="active"><?php echo e($category->name); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- breadcrumb end -->


        <!-- shop-area -->
        <div class="shop-area2 py-90">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-12">
                        <div class="col-md-12">
                            <div class="shop-sort">
                                <div class="shop-sort-box">
                                    <div class="shop-sorty-label">Sort By:</div>
                                    <select class="select" id="sort-products">
                                        <option value="default" <?php echo e(request('sort') == 'default' || !request('sort') ? 'selected' : ''); ?>>Default Sorting</option>
                                        <option value="latest" <?php echo e(request('sort') == 'latest' ? 'selected' : ''); ?>>Latest
                                            Items</option>
                                        <option value="bestseller" <?php echo e(request('sort') == 'bestseller' ? 'selected' : ''); ?>>
                                            Best Seller Items</option>
                                        <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price
                                            - Low To High</option>
                                        <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>
                                            Price - High To Low</option>
                                    </select>
                                    <div class="shop-sort-show">Showing <?php echo e($products->firstItem() ?? 0); ?> to
                                        <?php echo e($products->lastItem() ?? 0); ?> of <?php echo e($products->total()); ?> Results
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="shop-item-wrap item-2">
                            <div class="row g-4">

                                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-6 col-lg-3">
                                        <div class="product-item">
                                            <div class="product-img">
                                                <a href="<?php echo e(route('product_view', $product->slug)); ?>">
                                                    <?php if($product->primary_image): ?>
                                                        <img src="<?php echo e(asset($product->primary_image->path)); ?>"
                                                            alt="<?php echo e($product->name); ?>">
                                                    <?php else: ?>
                                                        <img src="<?php echo e(asset('assets/img/product/04.png')); ?>"
                                                            alt="<?php echo e($product->name); ?>">
                                                    <?php endif; ?>
                                                </a>
                                                <div class="product-action-wrap">
                                                    <div class="product-action">
                                                        <a href="#" data-bs-toggle="modal" data-bs-target="#quickview"
                                                            data-json="<?php echo e(htmlspecialchars(json_encode($product), ENT_QUOTES, 'UTF-8')); ?>"
                                                            data-tooltip="tooltip" title="Quick View"><i
                                                                class="far fa-eye"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="product-content">
                                                <h3 class="product-title"><a href="shop-single.html"><?php echo e($product->name); ?></a>
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <!-- pagination -->
                        <div class="pagination-area mt-50">
                            <div aria-label="Page navigation">
                                <?php echo e($products->links('vendor.pagination.bootstrap-5')); ?>

                            </div>
                            <div class="shop-sort-show mt-3">
                                Showing <?php echo e($products->firstItem() ?? 0); ?> to <?php echo e($products->lastItem() ?? 0); ?>

                                of <?php echo e($products->total()); ?> Products
                            </div>
                        </div>
                        <!-- pagination end -->
                    </div>

                </div>
            </div>
        </div>
        <!-- shop-area end -->

    </main>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Handle sort dropdown change
            const sortDropdown = document.getElementById('sort-products');
            if (sortDropdown) {
                sortDropdown.addEventListener('change', function () {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('sort', this.value);
                    window.location.href = currentUrl.toString();
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.front', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\furnitureecom\resources\views/pages/category.blade.php ENDPATH**/ ?>